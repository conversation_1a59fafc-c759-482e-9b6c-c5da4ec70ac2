google-adk==0.5.0
google-genai==1.14.0
google-api-python-client>=2.169.0
google-auth>=2.40.1
annotated-types==0.7.0
anyio==4.9.0
Authlib==1.5.2
cachetools==5.5.2
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.0
cryptography==44.0.3
Deprecated==1.2.18
docstring_parser==0.16
fastapi==0.115.12
google-adk==0.5.0
google-api-core==2.24.2
google-api-python-client==2.169.0
google-auth==2.40.1
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.2
google-cloud-aiplatform==1.92.0
google-cloud-bigquery==3.32.0
google-cloud-core==2.4.3
google-cloud-resource-manager==1.14.2
google-cloud-secret-manager==2.23.3
google-cloud-speech==2.32.0
google-cloud-storage==2.19.0
google-cloud-trace==1.16.1
google-crc32c==1.7.1
google-genai==1.14.0
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
graphviz==0.20.3
grpc-google-iam-v1==0.14.2
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.16.0
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.0
idna==3.10
importlib_metadata==8.6.1
mcp==1.8.1
numpy==2.2.5
oauthlib==3.2.2
opentelemetry-api==1.33.0
opentelemetry-exporter-gcp-trace==1.9.0
opentelemetry-resourcedetector-gcp==1.9.0a0
opentelemetry-sdk==1.33.0
opentelemetry-semantic-conventions==0.54b0
packaging==25.0
proto-plus==1.26.1
protobuf==5.29.4
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.4
pydantic-settings==2.9.1
pydantic_core==2.33.2
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
PyYAML==6.0.2
requests==2.32.3
requests-oauthlib==2.0.0
rsa==4.9.1
shapely==2.1.0
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.40
sse-starlette==2.3.4
starlette==0.46.2
typing-inspection==0.4.0
typing_extensions==4.13.2
tzlocal==5.3.1
uritemplate==4.1.1
urllib3==2.4.0
uvicorn==0.34.2
websockets==15.0.1
wrapt==1.17.2
zipp==3.21.0
