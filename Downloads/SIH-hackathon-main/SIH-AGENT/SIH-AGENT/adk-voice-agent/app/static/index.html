<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Assistant</title>
    <script src="/static/js/app.js" type="module"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
      :root {
        --primary-color: #4285F4;
        --secondary-color: #34A853;
        --accent-color: #EA4335;
        --background-color: #F8F9FA;
        --text-color: #202124;
        --gray-light: #E8EAED;
        --gray-medium: #BDC1C6;
      }
      
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background-color: var(--background-color);
        color: var(--text-color);
        line-height: 1.6;
        padding: 20px;
        max-width: 800px;
        margin: 0 auto;
      }
      
      header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid var(--gray-light);
      }
      
      h1 {
        font-size: 24px;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 8px;
      }
      
      .subtitle {
        font-size: 14px;
        color: #5F6368;
      }
      
      .chat-container {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        margin-bottom: 20px;
      }
      
      #messages {
        height: 400px;
        overflow-y: auto;
        padding: 20px;
        background-color: white;
        display: flex;
        flex-direction: column;
        scroll-behavior: smooth;
      }
      
      #messages p {
        margin-bottom: 16px;
        padding: 12px 16px;
        border-radius: 8px;
        max-width: 85%;
        word-wrap: break-word;
      }
      
      #messages p:last-child {
        margin-bottom: 0;
      }
      
      .agent-message {
        background-color: var(--gray-light);
        align-self: flex-start;
        border-bottom-left-radius: 2px;
        animation: fadeIn 0.3s ease-out;
      }
      
      /* Add a special style for messages that have audio */
      .audio-enabled .agent-message {
        border-left: 3px solid var(--secondary-color);
        padding-left: 14px;
      }
      
      /* Add a small audio icon for messages with audio */
      .audio-icon {
        display: inline-block;
        width: 18px;
        height: 18px;
        margin-right: 8px;
        vertical-align: middle;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2334A853'%3E%3Cpath d='M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
      }
      
      .user-message {
        background-color: var(--primary-color);
        color: white;
        align-self: flex-end;
        margin-left: auto;
        border-bottom-right-radius: 2px;
        animation: fadeIn 0.3s ease-out;
      }
      
      #messageForm {
        display: flex;
        gap: 10px;
        padding: 16px;
        background-color: white;
        border-top: 1px solid var(--gray-light);
      }
      
      #message {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid var(--gray-medium);
        border-radius: 24px;
        font-size: 16px;
        outline: none;
        transition: border-color 0.2s ease;
      }
      
      #message:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.3);
      }
      
      button {
        padding: 12px 20px;
        border: none;
        border-radius: 24px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }
      
      #sendButton {
        background-color: var(--primary-color);
        color: white;
      }
      
      #sendButton:disabled {
        background-color: var(--gray-medium);
        cursor: not-allowed;
      }
      
      #startAudioButton {
        background-color: var(--secondary-color);
        color: white;
      }
      
      #startAudioButton:disabled {
        background-color: var(--gray-medium);
        cursor: not-allowed;
      }
      
      #stopAudioButton {
        background-color: var(--accent-color);
        color: white;
        display: none;
      }
      
      .status-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 10px;
        font-size: 14px;
        color: #5F6368;
        gap: 20px;
      }
      
      .status-item {
        display: flex;
        align-items: center;
      }
      
      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: var(--gray-medium);
        margin-right: 8px;
      }
      
      .status-dot.connected {
        background-color: var(--secondary-color);
      }
      
      .status-dot.recording {
        background-color: var(--accent-color);
      }
      
      .recording-active {
        position: relative;
      }
      
      @keyframes pulse-recording {
        0% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.5; transform: scale(1.2); }
        100% { opacity: 1; transform: scale(1); }
      }
      
      @media (max-width: 600px) {
        body {
          padding: 10px;
        }
        
        #messages {
          height: 350px;
        }
        
        #messageForm {
          flex-direction: column;
        }
        
        button {
          width: 100%;
        }
      }
      
      /* Add animations */
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
      
      button:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
      
      button:active:not(:disabled) {
        transform: translateY(0);
      }
      
      #startAudioButton:hover:not(:disabled) {
        animation: pulse 1s infinite;
      }
      
      /* Add a typing indicator */
      .typing-indicator {
        display: flex;
        padding: 12px 16px;
        background-color: var(--gray-light);
        border-radius: 8px;
        width: fit-content;
        margin-bottom: 16px;
        align-self: flex-start;
        opacity: 0;
        transition: opacity 0.3s ease;
      }
      
      .typing-indicator.visible {
        opacity: 1;
      }
      
      .typing-indicator span {
        height: 8px;
        width: 8px;
        background-color: #70757A;
        border-radius: 50%;
        display: inline-block;
        margin: 0 2px;
      }
      
      .typing-indicator span:nth-child(1) {
        animation: bounce 1.2s infinite 0.2s;
      }
      
      .typing-indicator span:nth-child(2) {
        animation: bounce 1.2s infinite 0.4s;
      }
      
      .typing-indicator span:nth-child(3) {
        animation: bounce 1.2s infinite 0.6s;
      }
      
      @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-5px); }
      }
    </style>
  </head>

  <body>
    <header>
      <h1>Voice Assistant</h1>
      <p class="subtitle">Powered by Google ADK</p>
    </header>
    
    <div class="chat-container">
      <div id="messages">
        <div id="typing-indicator" class="typing-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      
      <form id="messageForm">
        <input type="text" id="message" name="message" placeholder="Type your message here..." autocomplete="off" />
        <button type="submit" id="sendButton" disabled>Send</button>
        <button type="button" id="startAudioButton">Enable Voice</button>
        <button type="button" id="stopAudioButton">Stop Voice</button>
      </form>
    </div>
    
    <div class="status-indicator">
      <div class="status-item">
        <div id="status-dot" class="status-dot"></div>
        <span id="connection-status">Connecting...</span>
      </div>
      <div class="status-item" id="recording-container" style="display: none;">
        <div class="status-dot recording" style="animation: pulse-recording 1.5s infinite;"></div>
        <span id="recording-status">Recording</span>
      </div>
    </div>
  </body>
</html>
