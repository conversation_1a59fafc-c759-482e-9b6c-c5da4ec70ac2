# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import logging
from typing import Any, Optional, Union
from google.genai import _common
from pydantic import Field
from typing_extensions import TypedDict

logger = logging.getLogger("google_genai.types")


class PairwiseChoice(_common.CaseInSensitiveEnum):
    """Output only. Pairwise metric choice."""

    PAIRWISE_CHOICE_UNSPECIFIED = "PAIRWISE_CHOICE_UNSPECIFIED"
    BASELINE = "BASELINE"
    CANDIDATE = "CANDIDATE"
    TIE = "TIE"


class BleuInstance(_common.BaseModel):
    """Bleu instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class BleuInstanceDict(TypedDict, total=False):
    """Bleu instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


BleuInstanceOrDict = Union[BleuInstance, BleuInstanceDict]


class BleuSpec(_common.BaseModel):
    """Spec for bleu metric."""

    use_effective_order: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use_effective_order to compute bleu score.""",
    )


class BleuSpecDict(TypedDict, total=False):
    """Spec for bleu metric."""

    use_effective_order: Optional[bool]
    """Optional. Whether to use_effective_order to compute bleu score."""


BleuSpecOrDict = Union[BleuSpec, BleuSpecDict]


class BleuInput(_common.BaseModel):

    instances: Optional[list[BleuInstance]] = Field(
        default=None, description="""Required. Repeated bleu instances."""
    )
    metric_spec: Optional[BleuSpec] = Field(
        default=None, description="""Required. Spec for bleu score metric."""
    )


class BleuInputDict(TypedDict, total=False):

    instances: Optional[list[BleuInstanceDict]]
    """Required. Repeated bleu instances."""

    metric_spec: Optional[BleuSpecDict]
    """Required. Spec for bleu score metric."""


BleuInputOrDict = Union[BleuInput, BleuInputDict]


class ExactMatchInstance(_common.BaseModel):
    """Exact match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ExactMatchInstanceDict(TypedDict, total=False):
    """Exact match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ExactMatchInstanceOrDict = Union[ExactMatchInstance, ExactMatchInstanceDict]


class ExactMatchSpec(_common.BaseModel):
    """Spec for exact match metric."""

    pass


class ExactMatchSpecDict(TypedDict, total=False):
    """Spec for exact match metric."""

    pass


ExactMatchSpecOrDict = Union[ExactMatchSpec, ExactMatchSpecDict]


class ExactMatchInput(_common.BaseModel):

    instances: Optional[list[ExactMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated exact match instances.""",
    )
    metric_spec: Optional[ExactMatchSpec] = Field(
        default=None, description="""Required. Spec for exact match metric."""
    )


class ExactMatchInputDict(TypedDict, total=False):

    instances: Optional[list[ExactMatchInstanceDict]]
    """Required. Repeated exact match instances."""

    metric_spec: Optional[ExactMatchSpecDict]
    """Required. Spec for exact match metric."""


ExactMatchInputOrDict = Union[ExactMatchInput, ExactMatchInputDict]


class RougeInstance(_common.BaseModel):
    """Rouge instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class RougeInstanceDict(TypedDict, total=False):
    """Rouge instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


RougeInstanceOrDict = Union[RougeInstance, RougeInstanceDict]


class RougeSpec(_common.BaseModel):
    """Spec for rouge metric."""

    rouge_type: Optional[str] = Field(
        default=None,
        description="""Optional. Supported rouge types are rougen[1-9], rougeL, and rougeLsum.""",
    )
    split_summaries: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to split summaries while using rougeLsum.""",
    )
    use_stemmer: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use stemmer to compute rouge score.""",
    )


class RougeSpecDict(TypedDict, total=False):
    """Spec for rouge metric."""

    rouge_type: Optional[str]
    """Optional. Supported rouge types are rougen[1-9], rougeL, and rougeLsum."""

    split_summaries: Optional[bool]
    """Optional. Whether to split summaries while using rougeLsum."""

    use_stemmer: Optional[bool]
    """Optional. Whether to use stemmer to compute rouge score."""


RougeSpecOrDict = Union[RougeSpec, RougeSpecDict]


class RougeInput(_common.BaseModel):
    """Rouge input."""

    instances: Optional[list[RougeInstance]] = Field(
        default=None, description="""Required. Repeated rouge instances."""
    )
    metric_spec: Optional[RougeSpec] = Field(
        default=None, description="""Required. Spec for rouge score metric."""
    )


class RougeInputDict(TypedDict, total=False):
    """Rouge input."""

    instances: Optional[list[RougeInstanceDict]]
    """Required. Repeated rouge instances."""

    metric_spec: Optional[RougeSpecDict]
    """Required. Spec for rouge score metric."""


RougeInputOrDict = Union[RougeInput, RougeInputDict]


class PointwiseMetricInstance(_common.BaseModel):
    """Pointwise metric instance."""

    json_instance: Optional[str] = Field(
        default=None,
        description="""Instance specified as a json string. String key-value pairs are expected in the json_instance to render PointwiseMetricSpec.instance_prompt_template.""",
    )


class PointwiseMetricInstanceDict(TypedDict, total=False):
    """Pointwise metric instance."""

    json_instance: Optional[str]
    """Instance specified as a json string. String key-value pairs are expected in the json_instance to render PointwiseMetricSpec.instance_prompt_template."""


PointwiseMetricInstanceOrDict = Union[
    PointwiseMetricInstance, PointwiseMetricInstanceDict
]


class PointwiseMetricSpec(_common.BaseModel):
    """Spec for pointwise metric."""

    metric_prompt_template: Optional[str] = Field(
        default=None,
        description="""Required. Metric prompt template for pointwise metric.""",
    )


class PointwiseMetricSpecDict(TypedDict, total=False):
    """Spec for pointwise metric."""

    metric_prompt_template: Optional[str]
    """Required. Metric prompt template for pointwise metric."""


PointwiseMetricSpecOrDict = Union[PointwiseMetricSpec, PointwiseMetricSpecDict]


class PointwiseMetricInput(_common.BaseModel):
    """Pointwise metric input."""

    instance: Optional[PointwiseMetricInstance] = Field(
        default=None, description="""Required. Pointwise metric instance."""
    )
    metric_spec: Optional[PointwiseMetricSpec] = Field(
        default=None, description="""Required. Spec for pointwise metric."""
    )


class PointwiseMetricInputDict(TypedDict, total=False):
    """Pointwise metric input."""

    instance: Optional[PointwiseMetricInstanceDict]
    """Required. Pointwise metric instance."""

    metric_spec: Optional[PointwiseMetricSpecDict]
    """Required. Spec for pointwise metric."""


PointwiseMetricInputOrDict = Union[PointwiseMetricInput, PointwiseMetricInputDict]


class PairwiseMetricInstance(_common.BaseModel):
    """Pairwise metric instance."""

    json_instance: Optional[str] = Field(
        default=None,
        description="""Instance specified as a json string. String key-value pairs are expected in the json_instance to render PairwiseMetricSpec.instance_prompt_template.""",
    )


class PairwiseMetricInstanceDict(TypedDict, total=False):
    """Pairwise metric instance."""

    json_instance: Optional[str]
    """Instance specified as a json string. String key-value pairs are expected in the json_instance to render PairwiseMetricSpec.instance_prompt_template."""


PairwiseMetricInstanceOrDict = Union[PairwiseMetricInstance, PairwiseMetricInstanceDict]


class PairwiseMetricSpec(_common.BaseModel):
    """Spec for pairwise metric."""

    metric_prompt_template: Optional[str] = Field(
        default=None,
        description="""Required. Metric prompt template for pairwise metric.""",
    )


class PairwiseMetricSpecDict(TypedDict, total=False):
    """Spec for pairwise metric."""

    metric_prompt_template: Optional[str]
    """Required. Metric prompt template for pairwise metric."""


PairwiseMetricSpecOrDict = Union[PairwiseMetricSpec, PairwiseMetricSpecDict]


class PairwiseMetricInput(_common.BaseModel):
    """Pairwise metric instance."""

    instance: Optional[PairwiseMetricInstance] = Field(
        default=None, description="""Required. Pairwise metric instance."""
    )
    metric_spec: Optional[PairwiseMetricSpec] = Field(
        default=None, description="""Required. Spec for pairwise metric."""
    )


class PairwiseMetricInputDict(TypedDict, total=False):
    """Pairwise metric instance."""

    instance: Optional[PairwiseMetricInstanceDict]
    """Required. Pairwise metric instance."""

    metric_spec: Optional[PairwiseMetricSpecDict]
    """Required. Spec for pairwise metric."""


PairwiseMetricInputOrDict = Union[PairwiseMetricInput, PairwiseMetricInputDict]


class ToolCallValidInstance(_common.BaseModel):
    """Tool call valid instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolCallValidInstanceDict(TypedDict, total=False):
    """Tool call valid instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolCallValidInstanceOrDict = Union[ToolCallValidInstance, ToolCallValidInstanceDict]


class ToolCallValidSpec(_common.BaseModel):
    """Spec for tool call valid metric."""

    pass


class ToolCallValidSpecDict(TypedDict, total=False):
    """Spec for tool call valid metric."""

    pass


ToolCallValidSpecOrDict = Union[ToolCallValidSpec, ToolCallValidSpecDict]


class ToolCallValidInput(_common.BaseModel):
    """Tool call valid input."""

    instances: Optional[list[ToolCallValidInstance]] = Field(
        default=None,
        description="""Required. Repeated tool call valid instances.""",
    )
    metric_spec: Optional[ToolCallValidSpec] = Field(
        default=None,
        description="""Required. Spec for tool call valid metric.""",
    )


class ToolCallValidInputDict(TypedDict, total=False):
    """Tool call valid input."""

    instances: Optional[list[ToolCallValidInstanceDict]]
    """Required. Repeated tool call valid instances."""

    metric_spec: Optional[ToolCallValidSpecDict]
    """Required. Spec for tool call valid metric."""


ToolCallValidInputOrDict = Union[ToolCallValidInput, ToolCallValidInputDict]


class ToolNameMatchInstance(_common.BaseModel):
    """Tool name match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolNameMatchInstanceDict(TypedDict, total=False):
    """Tool name match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolNameMatchInstanceOrDict = Union[ToolNameMatchInstance, ToolNameMatchInstanceDict]


class ToolNameMatchSpec(_common.BaseModel):
    """Spec for tool name match metric."""

    pass


class ToolNameMatchSpecDict(TypedDict, total=False):
    """Spec for tool name match metric."""

    pass


ToolNameMatchSpecOrDict = Union[ToolNameMatchSpec, ToolNameMatchSpecDict]


class ToolNameMatchInput(_common.BaseModel):
    """Tool name match input."""

    instances: Optional[list[ToolNameMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool name match instances.""",
    )
    metric_spec: Optional[ToolNameMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool name match metric.""",
    )


class ToolNameMatchInputDict(TypedDict, total=False):
    """Tool name match input."""

    instances: Optional[list[ToolNameMatchInstanceDict]]
    """Required. Repeated tool name match instances."""

    metric_spec: Optional[ToolNameMatchSpecDict]
    """Required. Spec for tool name match metric."""


ToolNameMatchInputOrDict = Union[ToolNameMatchInput, ToolNameMatchInputDict]


class ToolParameterKeyMatchInstance(_common.BaseModel):
    """Tool parameter key match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolParameterKeyMatchInstanceDict(TypedDict, total=False):
    """Tool parameter key match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolParameterKeyMatchInstanceOrDict = Union[
    ToolParameterKeyMatchInstance, ToolParameterKeyMatchInstanceDict
]


class ToolParameterKeyMatchSpec(_common.BaseModel):
    """Spec for tool parameter key match metric."""

    pass


class ToolParameterKeyMatchSpecDict(TypedDict, total=False):
    """Spec for tool parameter key match metric."""

    pass


ToolParameterKeyMatchSpecOrDict = Union[
    ToolParameterKeyMatchSpec, ToolParameterKeyMatchSpecDict
]


class ToolParameterKeyMatchInput(_common.BaseModel):
    """Tool parameter key match input."""

    instances: Optional[list[ToolParameterKeyMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool parameter key match instances.""",
    )
    metric_spec: Optional[ToolParameterKeyMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool parameter key match metric.""",
    )


class ToolParameterKeyMatchInputDict(TypedDict, total=False):
    """Tool parameter key match input."""

    instances: Optional[list[ToolParameterKeyMatchInstanceDict]]
    """Required. Repeated tool parameter key match instances."""

    metric_spec: Optional[ToolParameterKeyMatchSpecDict]
    """Required. Spec for tool parameter key match metric."""


ToolParameterKeyMatchInputOrDict = Union[
    ToolParameterKeyMatchInput, ToolParameterKeyMatchInputDict
]


class ToolParameterKVMatchInstance(_common.BaseModel):
    """Tool parameter kv match instance."""

    prediction: Optional[str] = Field(
        default=None, description="""Required. Output of the evaluated model."""
    )
    reference: Optional[str] = Field(
        default=None,
        description="""Required. Ground truth used to compare against the prediction.""",
    )


class ToolParameterKVMatchInstanceDict(TypedDict, total=False):
    """Tool parameter kv match instance."""

    prediction: Optional[str]
    """Required. Output of the evaluated model."""

    reference: Optional[str]
    """Required. Ground truth used to compare against the prediction."""


ToolParameterKVMatchInstanceOrDict = Union[
    ToolParameterKVMatchInstance, ToolParameterKVMatchInstanceDict
]


class ToolParameterKVMatchSpec(_common.BaseModel):
    """Spec for tool parameter kv match metric."""

    use_strict_string_match: Optional[bool] = Field(
        default=None,
        description="""Optional. Whether to use STRICT string match on parameter values.""",
    )


class ToolParameterKVMatchSpecDict(TypedDict, total=False):
    """Spec for tool parameter kv match metric."""

    use_strict_string_match: Optional[bool]
    """Optional. Whether to use STRICT string match on parameter values."""


ToolParameterKVMatchSpecOrDict = Union[
    ToolParameterKVMatchSpec, ToolParameterKVMatchSpecDict
]


class ToolParameterKVMatchInput(_common.BaseModel):
    """Tool parameter kv match input."""

    instances: Optional[list[ToolParameterKVMatchInstance]] = Field(
        default=None,
        description="""Required. Repeated tool parameter key value match instances.""",
    )
    metric_spec: Optional[ToolParameterKVMatchSpec] = Field(
        default=None,
        description="""Required. Spec for tool parameter key value match metric.""",
    )


class ToolParameterKVMatchInputDict(TypedDict, total=False):
    """Tool parameter kv match input."""

    instances: Optional[list[ToolParameterKVMatchInstanceDict]]
    """Required. Repeated tool parameter key value match instances."""

    metric_spec: Optional[ToolParameterKVMatchSpecDict]
    """Required. Spec for tool parameter key value match metric."""


ToolParameterKVMatchInputOrDict = Union[
    ToolParameterKVMatchInput, ToolParameterKVMatchInputDict
]


class HttpOptions(_common.BaseModel):
    """HTTP options to be used in each of the requests."""

    base_url: Optional[str] = Field(
        default=None,
        description="""The base URL for the AI platform service endpoint.""",
    )
    api_version: Optional[str] = Field(
        default=None, description="""Specifies the version of the API to use."""
    )
    headers: Optional[dict[str, str]] = Field(
        default=None,
        description="""Additional HTTP headers to be sent with the request.""",
    )
    timeout: Optional[int] = Field(
        default=None, description="""Timeout for the request in milliseconds."""
    )
    client_args: Optional[dict[str, Any]] = Field(
        default=None, description="""Args passed to the HTTP client."""
    )
    async_client_args: Optional[dict[str, Any]] = Field(
        default=None, description="""Args passed to the async HTTP client."""
    )


class HttpOptionsDict(TypedDict, total=False):
    """HTTP options to be used in each of the requests."""

    base_url: Optional[str]
    """The base URL for the AI platform service endpoint."""

    api_version: Optional[str]
    """Specifies the version of the API to use."""

    headers: Optional[dict[str, str]]
    """Additional HTTP headers to be sent with the request."""

    timeout: Optional[int]
    """Timeout for the request in milliseconds."""

    client_args: Optional[dict[str, Any]]
    """Args passed to the HTTP client."""

    async_client_args: Optional[dict[str, Any]]
    """Args passed to the async HTTP client."""


HttpOptionsOrDict = Union[HttpOptions, HttpOptionsDict]


class EvaluateInstancesConfig(_common.BaseModel):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptions] = Field(
        default=None, description="""Used to override HTTP request options."""
    )


class EvaluateInstancesConfigDict(TypedDict, total=False):
    """Config for evaluate instances."""

    http_options: Optional[HttpOptionsDict]
    """Used to override HTTP request options."""


EvaluateInstancesConfigOrDict = Union[
    EvaluateInstancesConfig, EvaluateInstancesConfigDict
]


class _EvaluateInstancesRequestParameters(_common.BaseModel):
    """Parameters for evaluating instances."""

    bleu_input: Optional[BleuInput] = Field(default=None, description="""""")
    exact_match_input: Optional[ExactMatchInput] = Field(
        default=None, description=""""""
    )
    rouge_input: Optional[RougeInput] = Field(default=None, description="""""")
    pointwise_metric_input: Optional[PointwiseMetricInput] = Field(
        default=None, description=""""""
    )
    pairwise_metric_input: Optional[PairwiseMetricInput] = Field(
        default=None, description=""""""
    )
    tool_call_valid_input: Optional[ToolCallValidInput] = Field(
        default=None, description=""""""
    )
    tool_name_match_input: Optional[ToolNameMatchInput] = Field(
        default=None, description=""""""
    )
    tool_parameter_key_match_input: Optional[ToolParameterKeyMatchInput] = Field(
        default=None, description=""""""
    )
    tool_parameter_kv_match_input: Optional[ToolParameterKVMatchInput] = Field(
        default=None, description=""""""
    )
    config: Optional[EvaluateInstancesConfig] = Field(default=None, description="""""")


class _EvaluateInstancesRequestParametersDict(TypedDict, total=False):
    """Parameters for evaluating instances."""

    bleu_input: Optional[BleuInputDict]
    """"""

    exact_match_input: Optional[ExactMatchInputDict]
    """"""

    rouge_input: Optional[RougeInputDict]
    """"""

    pointwise_metric_input: Optional[PointwiseMetricInputDict]
    """"""

    pairwise_metric_input: Optional[PairwiseMetricInputDict]
    """"""

    tool_call_valid_input: Optional[ToolCallValidInputDict]
    """"""

    tool_name_match_input: Optional[ToolNameMatchInputDict]
    """"""

    tool_parameter_key_match_input: Optional[ToolParameterKeyMatchInputDict]
    """"""

    tool_parameter_kv_match_input: Optional[ToolParameterKVMatchInputDict]
    """"""

    config: Optional[EvaluateInstancesConfigDict]
    """"""


_EvaluateInstancesRequestParametersOrDict = Union[
    _EvaluateInstancesRequestParameters, _EvaluateInstancesRequestParametersDict
]


class BleuMetricValue(_common.BaseModel):
    """Bleu metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Bleu score."""
    )


class BleuMetricValueDict(TypedDict, total=False):
    """Bleu metric value for an instance."""

    score: Optional[float]
    """Output only. Bleu score."""


BleuMetricValueOrDict = Union[BleuMetricValue, BleuMetricValueDict]


class BleuResults(_common.BaseModel):
    """Results for bleu metric."""

    bleu_metric_values: Optional[list[BleuMetricValue]] = Field(
        default=None, description="""Output only. Bleu metric values."""
    )


class BleuResultsDict(TypedDict, total=False):
    """Results for bleu metric."""

    bleu_metric_values: Optional[list[BleuMetricValueDict]]
    """Output only. Bleu metric values."""


BleuResultsOrDict = Union[BleuResults, BleuResultsDict]


class ExactMatchMetricValue(_common.BaseModel):
    """Exact match metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Exact match score."""
    )


class ExactMatchMetricValueDict(TypedDict, total=False):
    """Exact match metric value for an instance."""

    score: Optional[float]
    """Output only. Exact match score."""


ExactMatchMetricValueOrDict = Union[ExactMatchMetricValue, ExactMatchMetricValueDict]


class ExactMatchResults(_common.BaseModel):
    """Results for exact match metric."""

    exact_match_metric_values: Optional[list[ExactMatchMetricValue]] = Field(
        default=None, description="""Output only. Exact match metric values."""
    )


class ExactMatchResultsDict(TypedDict, total=False):
    """Results for exact match metric."""

    exact_match_metric_values: Optional[list[ExactMatchMetricValueDict]]
    """Output only. Exact match metric values."""


ExactMatchResultsOrDict = Union[ExactMatchResults, ExactMatchResultsDict]


class PairwiseMetricResult(_common.BaseModel):
    """Spec for pairwise metric result."""

    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for pairwise metric score.""",
    )
    pairwise_choice: Optional[PairwiseChoice] = Field(
        default=None, description="""Output only. Pairwise metric choice."""
    )


class PairwiseMetricResultDict(TypedDict, total=False):
    """Spec for pairwise metric result."""

    explanation: Optional[str]
    """Output only. Explanation for pairwise metric score."""

    pairwise_choice: Optional[PairwiseChoice]
    """Output only. Pairwise metric choice."""


PairwiseMetricResultOrDict = Union[PairwiseMetricResult, PairwiseMetricResultDict]


class PointwiseMetricResult(_common.BaseModel):
    """Spec for pointwise metric result."""

    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for pointwise metric score.""",
    )
    score: Optional[float] = Field(
        default=None, description="""Output only. Pointwise metric score."""
    )


class PointwiseMetricResultDict(TypedDict, total=False):
    """Spec for pointwise metric result."""

    explanation: Optional[str]
    """Output only. Explanation for pointwise metric score."""

    score: Optional[float]
    """Output only. Pointwise metric score."""


PointwiseMetricResultOrDict = Union[PointwiseMetricResult, PointwiseMetricResultDict]


class RougeMetricValue(_common.BaseModel):
    """Rouge metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Rouge score."""
    )


class RougeMetricValueDict(TypedDict, total=False):
    """Rouge metric value for an instance."""

    score: Optional[float]
    """Output only. Rouge score."""


RougeMetricValueOrDict = Union[RougeMetricValue, RougeMetricValueDict]


class RougeResults(_common.BaseModel):
    """Results for rouge metric."""

    rouge_metric_values: Optional[list[RougeMetricValue]] = Field(
        default=None, description="""Output only. Rouge metric values."""
    )


class RougeResultsDict(TypedDict, total=False):
    """Results for rouge metric."""

    rouge_metric_values: Optional[list[RougeMetricValueDict]]
    """Output only. Rouge metric values."""


RougeResultsOrDict = Union[RougeResults, RougeResultsDict]


class SummarizationVerbosityResult(_common.BaseModel):
    """Spec for summarization verbosity result."""

    confidence: Optional[float] = Field(
        default=None,
        description="""Output only. Confidence for summarization verbosity score.""",
    )
    explanation: Optional[str] = Field(
        default=None,
        description="""Output only. Explanation for summarization verbosity score.""",
    )
    score: Optional[float] = Field(
        default=None,
        description="""Output only. Summarization Verbosity score.""",
    )


class SummarizationVerbosityResultDict(TypedDict, total=False):
    """Spec for summarization verbosity result."""

    confidence: Optional[float]
    """Output only. Confidence for summarization verbosity score."""

    explanation: Optional[str]
    """Output only. Explanation for summarization verbosity score."""

    score: Optional[float]
    """Output only. Summarization Verbosity score."""


SummarizationVerbosityResultOrDict = Union[
    SummarizationVerbosityResult, SummarizationVerbosityResultDict
]


class ToolCallValidMetricValue(_common.BaseModel):
    """Tool call valid metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Tool call valid score."""
    )


class ToolCallValidMetricValueDict(TypedDict, total=False):
    """Tool call valid metric value for an instance."""

    score: Optional[float]
    """Output only. Tool call valid score."""


ToolCallValidMetricValueOrDict = Union[
    ToolCallValidMetricValue, ToolCallValidMetricValueDict
]


class ToolCallValidResults(_common.BaseModel):
    """Results for tool call valid metric."""

    tool_call_valid_metric_values: Optional[list[ToolCallValidMetricValue]] = Field(
        default=None,
        description="""Output only. Tool call valid metric values.""",
    )


class ToolCallValidResultsDict(TypedDict, total=False):
    """Results for tool call valid metric."""

    tool_call_valid_metric_values: Optional[list[ToolCallValidMetricValueDict]]
    """Output only. Tool call valid metric values."""


ToolCallValidResultsOrDict = Union[ToolCallValidResults, ToolCallValidResultsDict]


class ToolNameMatchMetricValue(_common.BaseModel):
    """Tool name match metric value for an instance."""

    score: Optional[float] = Field(
        default=None, description="""Output only. Tool name match score."""
    )


class ToolNameMatchMetricValueDict(TypedDict, total=False):
    """Tool name match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool name match score."""


ToolNameMatchMetricValueOrDict = Union[
    ToolNameMatchMetricValue, ToolNameMatchMetricValueDict
]


class ToolNameMatchResults(_common.BaseModel):
    """Results for tool name match metric."""

    tool_name_match_metric_values: Optional[list[ToolNameMatchMetricValue]] = Field(
        default=None,
        description="""Output only. Tool name match metric values.""",
    )


class ToolNameMatchResultsDict(TypedDict, total=False):
    """Results for tool name match metric."""

    tool_name_match_metric_values: Optional[list[ToolNameMatchMetricValueDict]]
    """Output only. Tool name match metric values."""


ToolNameMatchResultsOrDict = Union[ToolNameMatchResults, ToolNameMatchResultsDict]


class ToolParameterKeyMatchMetricValue(_common.BaseModel):
    """Tool parameter key match metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. Tool parameter key match score.""",
    )


class ToolParameterKeyMatchMetricValueDict(TypedDict, total=False):
    """Tool parameter key match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool parameter key match score."""


ToolParameterKeyMatchMetricValueOrDict = Union[
    ToolParameterKeyMatchMetricValue, ToolParameterKeyMatchMetricValueDict
]


class ToolParameterKeyMatchResults(_common.BaseModel):
    """Results for tool parameter key match metric."""

    tool_parameter_key_match_metric_values: Optional[
        list[ToolParameterKeyMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. Tool parameter key match metric values.""",
    )


class ToolParameterKeyMatchResultsDict(TypedDict, total=False):
    """Results for tool parameter key match metric."""

    tool_parameter_key_match_metric_values: Optional[
        list[ToolParameterKeyMatchMetricValueDict]
    ]
    """Output only. Tool parameter key match metric values."""


ToolParameterKeyMatchResultsOrDict = Union[
    ToolParameterKeyMatchResults, ToolParameterKeyMatchResultsDict
]


class ToolParameterKVMatchMetricValue(_common.BaseModel):
    """Tool parameter key value match metric value for an instance."""

    score: Optional[float] = Field(
        default=None,
        description="""Output only. Tool parameter key value match score.""",
    )


class ToolParameterKVMatchMetricValueDict(TypedDict, total=False):
    """Tool parameter key value match metric value for an instance."""

    score: Optional[float]
    """Output only. Tool parameter key value match score."""


ToolParameterKVMatchMetricValueOrDict = Union[
    ToolParameterKVMatchMetricValue, ToolParameterKVMatchMetricValueDict
]


class ToolParameterKVMatchResults(_common.BaseModel):
    """Results for tool parameter key value match metric."""

    tool_parameter_kv_match_metric_values: Optional[
        list[ToolParameterKVMatchMetricValue]
    ] = Field(
        default=None,
        description="""Output only. Tool parameter key value match metric values.""",
    )


class ToolParameterKVMatchResultsDict(TypedDict, total=False):
    """Results for tool parameter key value match metric."""

    tool_parameter_kv_match_metric_values: Optional[
        list[ToolParameterKVMatchMetricValueDict]
    ]
    """Output only. Tool parameter key value match metric values."""


ToolParameterKVMatchResultsOrDict = Union[
    ToolParameterKVMatchResults, ToolParameterKVMatchResultsDict
]


class EvaluateInstancesResponse(_common.BaseModel):
    """Result of evaluating an LLM metric."""

    bleu_results: Optional[BleuResults] = Field(
        default=None, description="""Results for bleu metric."""
    )
    exact_match_results: Optional[ExactMatchResults] = Field(
        default=None,
        description="""Auto metric evaluation results. Results for exact match metric.""",
    )
    pairwise_metric_result: Optional[PairwiseMetricResult] = Field(
        default=None, description="""Result for pairwise metric."""
    )
    pointwise_metric_result: Optional[PointwiseMetricResult] = Field(
        default=None,
        description="""Generic metrics. Result for pointwise metric.""",
    )
    rouge_results: Optional[RougeResults] = Field(
        default=None, description="""Results for rouge metric."""
    )
    summarization_verbosity_result: Optional[SummarizationVerbosityResult] = Field(
        default=None,
        description="""Result for summarization verbosity metric.""",
    )
    tool_call_valid_results: Optional[ToolCallValidResults] = Field(
        default=None,
        description="""Tool call metrics. Results for tool call valid metric.""",
    )
    tool_name_match_results: Optional[ToolNameMatchResults] = Field(
        default=None, description="""Results for tool name match metric."""
    )
    tool_parameter_key_match_results: Optional[ToolParameterKeyMatchResults] = Field(
        default=None,
        description="""Results for tool parameter key match metric.""",
    )
    tool_parameter_kv_match_results: Optional[ToolParameterKVMatchResults] = Field(
        default=None,
        description="""Results for tool parameter key value match metric.""",
    )


class EvaluateInstancesResponseDict(TypedDict, total=False):
    """Result of evaluating an LLM metric."""

    bleu_results: Optional[BleuResultsDict]
    """Results for bleu metric."""

    exact_match_results: Optional[ExactMatchResultsDict]
    """Auto metric evaluation results. Results for exact match metric."""

    pairwise_metric_result: Optional[PairwiseMetricResultDict]
    """Result for pairwise metric."""

    pointwise_metric_result: Optional[PointwiseMetricResultDict]
    """Generic metrics. Result for pointwise metric."""

    rouge_results: Optional[RougeResultsDict]
    """Results for rouge metric."""

    summarization_verbosity_result: Optional[SummarizationVerbosityResultDict]
    """Result for summarization verbosity metric."""

    tool_call_valid_results: Optional[ToolCallValidResultsDict]
    """Tool call metrics. Results for tool call valid metric."""

    tool_name_match_results: Optional[ToolNameMatchResultsDict]
    """Results for tool name match metric."""

    tool_parameter_key_match_results: Optional[ToolParameterKeyMatchResultsDict]
    """Results for tool parameter key match metric."""

    tool_parameter_kv_match_results: Optional[ToolParameterKVMatchResultsDict]
    """Results for tool parameter key value match metric."""


EvaluateInstancesResponseOrDict = Union[
    EvaluateInstancesResponse, EvaluateInstancesResponseDict
]


class EvalDataset(_common.BaseModel):

    file: Optional[str] = Field(default=None, description="""""")


class EvalDatasetDict(TypedDict, total=False):

    file: Optional[str]
    """"""


EvalDatasetOrDict = Union[EvalDataset, EvalDatasetDict]
