Metadata-Version: 2.1
Name: opentelemetry-exporter-gcp-trace
Version: 1.9.0
Summary: Google Cloud Trace exporter for OpenTelemetry
Home-page: https://github.com/GoogleCloudPlatform/opentelemetry-operations-python/tree/main/opentelemetry-exporter-gcp-trace
Author: Google
Author-email: <EMAIL>
License: Apache-2.0
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: google-cloud-trace ~=1.1
Requires-Dist: opentelemetry-api ~=1.0
Requires-Dist: opentelemetry-resourcedetector-gcp ==1.*,>=1.5.0dev0
Requires-Dist: opentelemetry-sdk ~=1.0
Provides-Extra: test

OpenTelemetry Google Cloud Integration
======================================

.. image:: https://badge.fury.io/py/opentelemetry-exporter-gcp-trace.svg
    :target: https://badge.fury.io/py/opentelemetry-exporter-gcp-trace

.. image:: https://readthedocs.org/projects/google-cloud-opentelemetry/badge/?version=latest
    :target: https://google-cloud-opentelemetry.readthedocs.io/en/latest/?badge=latest
    :alt: Documentation Status

This library provides support for exporting traces to Google Cloud Trace.

To get started with instrumentation in Google Cloud, see `Generate traces and metrics with
Python <https://cloud.google.com/stackdriver/docs/instrumentation/setup/python>`_.

To learn more about instrumentation and observability, including opinionated recommendations
for Google Cloud Observability, visit `Instrumentation and observability
<https://cloud.google.com/stackdriver/docs/instrumentation/overview>`_.

For resource detection and GCP trace context propagation, see
`opentelemetry-tools-google-cloud
<https://pypi.org/project/opentelemetry-tools-google-cloud/>`_. For the
Google Cloud Monitoring exporter, see
`opentelemetry-exporter-gcp-monitoring
<https://pypi.org/project/opentelemetry-exporter-gcp-monitoring/>`_.

Installation
------------

.. code:: bash

    pip install opentelemetry-exporter-gcp-trace

Usage
-----

.. code:: python

    from opentelemetry import trace
    from opentelemetry.exporter.cloud_trace import CloudTraceSpanExporter
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import (
        SimpleSpanProcessor,
    )

    trace.set_tracer_provider(TracerProvider())

    cloud_trace_exporter = CloudTraceSpanExporter(
        project_id='my-gcloud-project',
    )
    trace.get_tracer_provider().add_span_processor(
        SimpleSpanProcessor(cloud_trace_exporter)
    )
    tracer = trace.get_tracer(__name__)
    with tracer.start_as_current_span('foo'):
        print('Hello world!')


References
----------

* `Cloud Trace <https://cloud.google.com/trace/>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
