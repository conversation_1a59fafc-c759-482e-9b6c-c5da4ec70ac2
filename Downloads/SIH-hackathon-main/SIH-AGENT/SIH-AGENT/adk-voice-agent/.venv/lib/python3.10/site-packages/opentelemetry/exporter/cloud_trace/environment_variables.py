# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

OTEL_EXPORTER_GCP_TRACE_PROJECT_ID = "OTEL_EXPORTER_GCP_TRACE_PROJECT_ID"
"""
.. envvar:: OTEL_EXPORTER_GCP_TRACE_PROJECT_ID

    GCP project ID for the project to send spans to. Equivalent to constructor parameter to
    :class:`opentelemetry.exporter.cloud_trace.CloudTraceSpanExporter`.
"""

OTEL_EXPORTER_GCP_TRACE_RESOURCE_REGEX = (
    "OTEL_EXPORTER_GCP_TRACE_RESOURCE_REGEX"
)
"""
.. envvar:: OTEL_EXPORTER_GCP_TRACE_RESOURCE_REGEX

    Resource attributes with keys matching this regex will be added to exported spans as labels
    :class:`opentelemetry.exporter.cloud_trace.CloudTraceSpanExporter`. Equivalent to constructor parameter to
    :class:`opentelemetry.exporter.cloud_trace.CloudTraceSpanExporter`.
"""
