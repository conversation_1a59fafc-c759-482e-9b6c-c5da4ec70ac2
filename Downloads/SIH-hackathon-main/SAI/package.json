{"name": "SAI", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.5", "@react-native-picker/picker": "^2.11.2", "@react-native/new-app-screen": "0.81.1", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "axios": "^1.12.2", "react": "19.1.0", "react-native": "0.81.1", "react-native-gesture-handler": "^2.28.0", "react-native-get-random-values": "^1.11.0", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-reanimated": "^4.1.0", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-toast-message": "^2.3.3", "react-native-tts": "^4.1.1", "react-native-vector-icons": "^10.3.0", "react-native-video": "^6.16.1", "react-native-vision-camera": "^4.7.2", "react-native-worklets": "^0.5.1", "react-native-worklets-core": "^1.6.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "20.0.0", "@react-native-community/cli-platform-android": "20.0.0", "@react-native-community/cli-platform-ios": "20.0.0", "@react-native/babel-preset": "0.81.1", "@react-native/eslint-config": "0.81.1", "@react-native/metro-config": "0.81.1", "@react-native/typescript-config": "0.81.1", "@react-navigation/native-stack": "^7.3.26", "@types/jest": "^29.5.13", "@types/react": "^19.1.13", "@types/react-native": "^0.72.8", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "concurrently": "^9.2.1", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "^0.77.0", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "typescript": "^5.9.2"}, "engines": {"node": ">=20"}}