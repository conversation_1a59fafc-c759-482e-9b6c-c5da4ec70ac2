{"mobile_summary": {"final_validity": "valid", "authenticity_confidence": 81.85, "performance_detected": true, "recommendation": "✅ Analysis complete - Video verified", "processing_speed": "mobile_optimized", "real_time_counting": true}, "performance_results": {"exercise_type": "squats", "rep_count": 15, "jump_count": 0, "exercise_results": {"rep_count": 15, "form_analysis": {"form_score": 85, "mobile_optimized": true, "rep_timestamps": [4.363187574514039, 5.695458589632829, 7.027729604751619, 8.39330739524838, 9.625658084233262, 10.824701997840172, 12.057052686825054, 13.256096600431965, 14.455140514038877, 15.620877652267819, 16.78661479049676, 17.91904515334773, 18.984861965442764, 20.117292328293736, 21.249722691144708]}, "keypoints_used": [11, 13, 15], "joint_names": ["hip", "knee", "ankle"], "exercise_completed": true, "mobile_optimized": true}, "output_video": "mobile_analysis_squats_1758380708.mp4", "video_properties": {"fps": 30.023921218787038, "total_frames": 1389, "duration": 46.263111, "resolution": "202x360", "mobile_optimized": true, "frames_processed": 695}, "mobile_performance": {"optimization_level": "high", "frame_processing_ratio": 1.0, "real_time_counting": true}}, "security_results": {"authenticity_status": "authentic", "risk_level": "very_low", "risk_score": 18.15, "confidence_score": 81.85, "face_verification": {"verified": true, "confidence": 74.07, "verification_rate": 33.3, "avg_similarity": 67.4, "max_similarity": 70.9, "successful_verifications": 3, "total_verifications": 9, "frames_processed": 3, "models_used": ["VGG-Face"], "balanced_verification": true, "detailed_results": [{"frame": 1, "reference": "WhatsApp Image 2025-09-13 at 15.54.43_d0024db4.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.7778, "threshold": 0.5, "similarity": 22.22, "verified": false}, {"frame": 1, "reference": "WhatsApp Image 2025-09-13 at 15.54.57_65ff91ab.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.6952, "threshold": 0.5, "similarity": 30.48, "verified": false}, {"frame": 1, "reference": "WhatsApp Image 2025-09-13 at 15.55.14_99ebe1ad.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.3474, "threshold": 0.5, "similarity": 65.26, "verified": true}, {"frame": 2, "reference": "WhatsApp Image 2025-09-13 at 15.54.43_d0024db4.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.7418, "threshold": 0.5, "similarity": 25.82, "verified": false}, {"frame": 2, "reference": "WhatsApp Image 2025-09-13 at 15.54.57_65ff91ab.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.6804, "threshold": 0.5, "similarity": 31.96, "verified": false}, {"frame": 2, "reference": "WhatsApp Image 2025-09-13 at 15.55.14_99ebe1ad.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.3396, "threshold": 0.5, "similarity": 66.04, "verified": true}, {"frame": 3, "reference": "WhatsApp Image 2025-09-13 at 15.54.43_d0024db4.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.731, "threshold": 0.5, "similarity": 26.9, "verified": false}, {"frame": 3, "reference": "WhatsApp Image 2025-09-13 at 15.54.57_65ff91ab.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.6839, "threshold": 0.5, "similarity": 31.61, "verified": false}, {"frame": 3, "reference": "WhatsApp Image 2025-09-13 at 15.55.14_99ebe1ad.jpg", "model": "VGG-Face", "metric": "cosine", "distance": 0.291, "threshold": 0.5, "similarity": 70.9, "verified": true}]}, "flag_analysis": {"total_flags": 0, "flag_categories": {}}, "recommendations": ["✅ VERIFIED: Face verification passed", "🔒 LEGITIMATE: Person identity confirmed"]}, "mobile_technical": {"cheat_detection_time": 57.32156419754028, "sports_analysis_time": 72.61383080482483, "optimization_level": "smartphone", "models_used": ["YOLO11n-nano", "DeepFace-mobile", "Mobile-Cheat-Detection"], "mobile_features": ["real_time_counting", "frame_skipping", "resolution_optimization", "memory_efficient", "fast_inference"], "analysis_timestamp": "2025-09-20T15:05:09.835531+00:00", "version": "mobile_v1.0"}, "total_processing_time": 131.62973618507385}