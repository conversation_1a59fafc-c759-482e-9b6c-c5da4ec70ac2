{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.12.2", "cloudinary": "^1.41.3", "dotenv": "^17.2.2", "express": "^5.1.0", "form-data": "^4.0.4", "joi": "^18.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.1", "multer": "^2.0.2", "multer-storage-cloudinary": "^4.0.0", "nodemon": "^3.1.10", "twilio": "^5.9.0"}}